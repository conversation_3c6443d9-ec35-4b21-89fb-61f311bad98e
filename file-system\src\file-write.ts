import * as fs from 'fs';
import * as path from 'path';
import { WriteFileTool } from '../baml_client/types';

export type FileSystemRead = WriteFileTool;

const ROOT_DIRECTORY = process.cwd();


/**
 * 验证路径是否在根目录内
 */
function validatePath(filePath: string): string {
    // If path is not absolute, resolve it relative to ROOT_DIRECTORY
    const absolutePath = path.isAbsolute(filePath) ?
        path.resolve(filePath) :
        path.resolve(ROOT_DIRECTORY, filePath);

    const rootPath = path.resolve(ROOT_DIRECTORY);

    if (!absolutePath.startsWith(rootPath)) {
        throw new Error(`Path ${filePath} is outside the allowed root directory ${rootPath}`);
    }

    return absolutePath;
}

/**
 * 通过检查文件的内容来检查文件是否是二进制文件
 */
function isBinaryFile(filePath: string): boolean {
    try {
        const buffer = fs.readFileSync(filePath);
        const chunk = buffer.slice(0, 512);
        
        // Check for null bytes which indicate binary content
        for (let i = 0; i < chunk.length; i++) {
            if (chunk[i] === 0) {
                return true;
            }
        }
        
        return false;
    } catch (error) {
        return false;
    }
}

/**
 * Gets the MIME type based on file extension
 */
function getMimeType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes: { [key: string]: string } = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml',
        '.bmp': 'image/bmp',
        '.pdf': 'application/pdf'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * Checks if a file is an image or PDF that should be returned as base64
 */
function isMediaFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp', '.pdf'].includes(ext);
}


/**
 * Writes content to a file
 */
export async function writeFile(tool: WriteFileTool): Promise<string> {
    try {
        const absolutePath = validatePath(tool.file_path);
        const fileExists = fs.existsSync(absolutePath);
        
        // Create parent directories if they don't exist
        const parentDir = path.dirname(absolutePath);
        if (!fs.existsSync(parentDir)) {
            fs.mkdirSync(parentDir, { recursive: true });
        }
        
        // Write the file
        fs.writeFileSync(absolutePath, tool.content, 'utf-8');
        
        if (fileExists) {
            return `Successfully overwrote file: ${tool.file_path}`;
        } else {
            return `Successfully created and wrote to new file: ${tool.file_path}`;
        }
        
    } catch (error) {
        return `Error writing file: ${error instanceof Error ? error.message : String(error)}`;
    }
}