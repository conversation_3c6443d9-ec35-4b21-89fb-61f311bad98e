name: ci

on: [push, pull_request]

jobs:
  legacy:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: ['0.10', '0.12', 4.x, 6.x, 8.x, 10.x, 12.x, 13.x, 14.x, 15.x, 16.x]

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install
        run: |
          npm install --production && npm install tape

      - name: Run tests
        run: |
          npm run test

  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x, 22.x]

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install
        run: |
          npm install

      - name: Run tests
        run: |
          npm run test:coverage

  types:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install
        run: |
          npm install

      - name: Run types tests
        run: |
          npm run test:typescript

  lint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install
        run: |
          npm install

      - name: Lint
        run: |
          npm run lint
